"""
Data loading utilities for neural data analysis.

This module provides functions for loading, preprocessing, and organizing neural datasets
for model training and analysis. It handles dataset loading, time embedding, train/validation
splitting, and calculation of spike-triggered statistics.

The module supports:
- Loading multiple dataset types and combining them
- Creating reproducible train/validation splits based on trials
- Calculating and caching spike-triggered averages and second moments
- Preprocessing datasets with custom functions
"""
import torch
import numpy as np
from .datasets import DictDataset, CombinedEmbeddedDataset
from .filtering import get_valid_dfs
from .splitting import split_inds_by_trial
from ..general import ensure_tensor
from ..io import get_session
from ..rf import calc_sta
from typing import Callable, Dict, List, Any
import yaml

def get_embedded_datasets(sess, types=None, keys_lags=None, train_val_split=None, cids=None, seed=1002, pre_func=None, **kwargs):
    """
    Create train and validation datasets from multiple dataset types with time embedding.

    This function loads multiple datasets, applies preprocessing, filters valid frames,
    splits them into training and validation sets based on trials, and combines them
    into embedded datasets ready for model training.

    Parameters
    ----------
    sess : Session object
        Session object containing path information
    types : list of str or dict datasets
        List of dataset types to load (e.g., ['gaborium', 'backimage'])
        or a list of DictDataset objects
    keys_lags : dict
        Dictionary mapping dataset keys to lag values for time embedding
        Example: {'robs': 0, 'stim': np.arange(10)}
    train_val_split : float
        Fraction of data to use for training (between 0 and 1)
    cids : array-like, optional
        Cell IDs to include. If None, all cells are included
    seed : int, optional
        Random seed for reproducible train/validation splits, default=1002
    pre_func : callable, optional
        Function to apply to each dataset after loading

    Returns
    -------
    train_dset : CombinedEmbeddedDataset
        Combined dataset for training
    val_dset : CombinedEmbeddedDataset
        Combined dataset for validation
    """
    # Determine maximum number of lags needed based on keys_lags
    n_lags = np.max([np.max(keys_lags[k]) for k in keys_lags])

    # Default preprocessing function if none provided
    if pre_func is None:
        def pre_func(x):
            # Normalize stimulus to [-0.5, 0.5] range
            x['stim'] = (x['stim'].float() - 127) / 255
            # Generate valid frame mask based on trial boundaries and DPI validity
            x['dfs'] = get_valid_dfs(x, n_lags)
            return x

    # Load and preprocess each dataset type
    dsets = []
    for dset_type in types:
        if isinstance(dset_type, DictDataset):
            dset = dset_type
        else:
            # Load dataset from file
            dset = DictDataset.load(sess.sess_dir / 'shifter' / f'{dset_type}_shifted.dset')

        # Apply preprocessing
        dset = pre_func(dset)
        
        # Filter by cell IDs if specified
        if cids is not None:
            dset['robs'] = dset['robs'][:,cids]
        dsets.append(dset)

    # Get indices of valid frames for each dataset
    dset_inds = [dset['dfs'].squeeze().nonzero(as_tuple=True)[0] for dset in dsets]

    # Print dataset statistics
    for iD, dset in enumerate(dsets):
        print(f'{types[iD]} dataset size: {len(dset)} / {len(dset_inds[iD])} ({len(dset_inds[iD])/len(dset)*100:.2f}%)')

    # Split indices into training and validation sets by trial
    train_inds, val_inds = [], []
    for iD, dset in enumerate(dsets):
        train_inds_, val_inds_ = split_inds_by_trial(dset, dset_inds[iD], train_val_split, seed)
        train_inds.append(train_inds_)
        val_inds.append(val_inds_)

    # Create combined embedded datasets for training and validation
    train_dset = CombinedEmbeddedDataset(dsets, train_inds, keys_lags)
    val_dset = CombinedEmbeddedDataset(dsets, val_inds, keys_lags)

    return train_dset, val_dset

def get_gaborium_sta_ste(sess, n_lags, cids=None):
    """
    Calculate or load cached spike-triggered averages (STAs) and spike-triggered
    second moments (STEs) for gaborium stimulus data.

    This function first checks if cached STAs/STEs exist and have sufficient lags.
    If so, it loads them from cache. Otherwise, it calculates them from the raw data
    and saves them to cache for future use.

    Parameters
    ----------
    sess : Session object
        Session object containing path information
    n_lags : int
        Number of time lags to calculate STAs/STEs for
    cids : array-like, optional
        Cell IDs to include. If None, all cells are included

    Returns
    -------
    stas : numpy.ndarray
        Spike-triggered averages with shape (n_cells, n_lags, n_y, n_x)
    stes : numpy.ndarray
        Spike-triggered second moments with shape (n_cells, n_lags, n_y, n_x)
    """
    # Verify that the dataset exists
    assert (sess.sess_dir / 'shifter' / 'gaborium_shifted.dset').exists()

    # Define cache file path
    cache_dir = sess.sess_dir / 'shifter' / 'gaborium_sta_ste.npy'

    # Try to load from cache if it exists
    if cache_dir.exists():
        stas, stes = np.load(cache_dir, allow_pickle=True)
        n_lags_cached = stas.shape[1]

        # If cached data has enough lags, use it
        if n_lags_cached >= n_lags:
            if cids is None:
                cids = np.arange(stas.shape[0])

            # Return requested subset of lags and cells
            return stas[cids][:,:n_lags], stes[cids][:,:n_lags]
        else:
            print(f'Cached STAs/STEs have {n_lags_cached} lags, but {n_lags} were requested. Recalculating...')
    else:
        print('Cached STAs/STEs not found. Calculating...')

    # Load and preprocess the dataset
    dset = DictDataset.load(sess.sess_dir / 'shifter' / 'gaborium_shifted.dset')
    dset['stim'] = dset['stim'].float()
    # Normalize stimulus (mean-centered)
    dset['stim'] = (dset['stim'] - dset['stim'].mean()) / 255
    # Generate valid frame mask
    dset['dfs'] = get_valid_dfs(dset, n_lags)

    # Calculate spike-triggered averages (STAs)
    stas = calc_sta(dset['stim'].detach().cpu(),
                   dset['robs'].cpu(),
                   range(n_lags),
                   dfs=dset['dfs'].cpu().squeeze(),
                   progress=True).cpu().squeeze().numpy()

    # Calculate spike-triggered second moments (STEs)
    # Uses squared stimulus values via stim_modifier
    stes = calc_sta(dset['stim'].detach().cpu(),
                   dset['robs'].cpu(),
                   range(n_lags),
                   dfs=dset['dfs'].cpu().squeeze(),
                   stim_modifier=lambda x: x**2,
                   progress=True).cpu().squeeze().numpy()

    # Save results to cache for future use
    np.save(cache_dir, [stas, stes])

    # Filter by cell IDs if specified
    if cids is not None:
        stas = stas[cids]
        stes = stes[cids]

    return stas, stes


# ──────────────────────────────────────────────────────────────────────────────
# 1.  Transform registry
# ──────────────────────────────────────────────────────────────────────────────
class TransformFn(Callable[[torch.Tensor], torch.Tensor]): ...
TRANSFORM_REGISTRY: Dict[str, Callable[[Dict[str, Any]], TransformFn]] = {}

def _register(name):
    def wrap(fn):
        TRANSFORM_REGISTRY[name] = fn
        return fn
    return wrap

@_register("pixelnorm")
def _make_pixelnorm(cfg):
    def pixelnorm(x: torch.Tensor):
        return (x.float() - 127) / 255
    return pixelnorm

@_register("diff")
def _make_diff(cfg):
    axis = cfg.get("axis", 0)
    def diff(x: torch.Tensor):
        # prepend first slice to keep length constant
        prepend = x.index_select(axis, torch.tensor([0], device=x.device))
        return torch.diff(x, dim=axis, prepend=prepend)
    return diff

@_register("mul")
def _make_mul(cfg):
    factor = cfg if isinstance(cfg, (int,float)) else cfg.get("factor", 1.0)
    def mul(x): return x * factor
    return mul

@_register("temporal_basis")
def _make_basis(cfg):
    from DataYatesV1.models.modules import TemporalBasis
    basis = TemporalBasis(**cfg)
    def tb(x):                         # x (T, …)  or (B,T, …)
        # TemporalBasis expects (B,C,T); reshape accordingly
        orig_shape = x.shape

        if x.ndim == 2:                # (T, C) → (1,C,T)
            x = x.transpose(0,1).unsqueeze(0)
        elif x.ndim == 3:              # (T, H, W) → (1, 1, T, H, W)
            x = x.unsqueeze(0).unsqueeze(0)
        else:
            raise ValueError("Unsupported tensor rank for temporal_basis")

        y = basis(x)                    # (B,C',T)
        if len(orig_shape) == 2:                # (1,C',T) → (T,C')
            y = y.permute(0,2,1).squeeze(0) # back to (T,C')
        elif len(orig_shape) == 3:  
            # (1, C, T, H, W) → (T, C, H, W)
            y = y.permute(2,0,1,3,4).reshape((orig_shape[0], -1, orig_shape[1], orig_shape[2])) # to (T, Cnew, H, W)

        return y.view(*y.shape)         # torchscript friendliness
    return tb

@_register("splitrelu")
def _make_splitrelu(cfg):
    from DataYatesV1.models.modules import SplitRelu
    return SplitRelu(**cfg)

# ──────────────────────────────────────────────────────────────────────────────
# 2.  Build a composite transform pipeline
# ──────────────────────────────────────────────────────────────────────────────
def make_pipeline(op_list: List[Dict[str, Any]]) -> TransformFn:
    fns: List[TransformFn] = []
    for op_dict in op_list:
        name, cfg = next(iter(op_dict.items()))
        if name not in TRANSFORM_REGISTRY:
            raise ValueError(f"Unknown transform '{name}'")
        fns.append(TRANSFORM_REGISTRY[name](cfg))

    def pipeline(x):
        for fn in fns:
            x = fn(x)
        return x
    return pipeline

# ──────────────────────────────────────────────────────────────────────────────
# 3.  The new prepare_data
# ──────────────────────────────────────────────────────────────────────────────
def prepare_data(dataset_config: Dict[str, Any]):
    """
    Extended prepare_data that supports a `transforms:` block with preprocessing.

    Parameters
    ----------
    dataset_config : dict
        Parsed YAML config (already loaded via yaml.safe_load).

    Returns
    -------
    train_dset, val_dset, dataset_config  (unchanged downstream interface)
    """
    print("\nPreparing data (with preprocessing)…")

    # check if dataset_config is a path
    if isinstance(dataset_config, str):
        with open(dataset_config, 'r') as f:
            dataset_config = yaml.safe_load(f)

    # -- unpack ----------------------------------------------------------------
    sess_name  = dataset_config["session"]
    dset_types = dataset_config["types"]
    transforms  = dataset_config.get("transforms", {})
    keys_lags  = dataset_config["keys_lags"]

    sess = get_session(*sess_name.split("_"))

    # -------------------------------------------------------------------------
    # Build transform specs once
    # -------------------------------------------------------------------------
    transform_specs = {}
    for var_name, spec in transforms.items():
        pipeline = make_pipeline(spec.get("ops", []))
        transform_specs[var_name] = dict(
            source     = spec.get("source", var_name),
            pipeline   = pipeline,
            expose_as  = spec.get("expose_as", var_name),
        )

        # Merge any per-variable keys_lags into the master dict
        if "keys_lags" in spec:
            keys_lags[spec["expose_as"]] = spec["keys_lags"]

    # -------------------------------------------------------------------------
    # Load each DictDataset, run transforms in-place, and stash
    # -------------------------------------------------------------------------
    n_lags = dataset_config.get("n_lags", np.max([np.max(keys_lags[k]) for k in keys_lags]))

    preprocessed_dsets = []
    for dt in dset_types:
        dset_path = sess.sess_dir / "shifter" / f"{dt}_shifted.dset"
        dset = DictDataset.load(dset_path)
        dset.metadata['name'] = dt # add name to metadata for later identification
        
        dset['dfs'] = get_valid_dfs(dset, n_lags)

        # -------------------------------------------------------------
        # variable-specific pipelines
        # -------------------------------------------------------------
        for vname, spec in transform_specs.items():

            src_key   = spec["source"]
            expose_as = spec["expose_as"]
            # print(f"Transforming {src_key} → {expose_as}")  
            data_tensor = ensure_tensor(dset[src_key])   # → torch.Tensor
            data_tensor = spec["pipeline"](data_tensor)
            dset[expose_as] = data_tensor

        preprocessed_dsets.append(dset)

    # -------------------------------------------------------------------------
    # Combine datasets
    # -------------------------------------------------------------------------
    train_dset, val_dset = get_embedded_datasets(
        sess,
        types            = preprocessed_dsets,           # pass in the preprocessed datasets
        keys_lags        = keys_lags,
        train_val_split  = dataset_config["train_val_split"],
        cids             = dataset_config.get("cids", None),
        seed             = dataset_config.get("seed", 1002),
        pre_func         = lambda x: x,          # preprocessing already done
    )

    print(f"Train size: {len(train_dset)} samples | "
          f"Val size: {len(val_dset)} samples")

    # IMPORTANT: pass behaviour feature dim back to model yaml --------------
    beh_keys = [v["expose_as"] for v in transform_specs.values()
                if v["expose_as"] == "behavior"]
    if beh_keys:
        # assume they were concatenated along last dim already
        sample = train_dset[0]["behavior"]
        dataset_config["behavior_dim"] = sample.shape[-1]

    return train_dset, val_dset, dataset_config
